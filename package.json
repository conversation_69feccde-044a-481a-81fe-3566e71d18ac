{"name": "gloopi-b2b-monorepo", "version": "1.0.0", "private": true, "description": "Gloopi B2B monorepo with Turbo", "author": "Gloopi Indonesia", "license": "MIT", "packageManager": "yarn@4.4.0", "workspaces": ["apps/*"], "scripts": {"build": "turbo build", "dev": "turbo dev", "start": "turbo start", "lint": "turbo lint", "test": "turbo test", "clean": "turbo clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "turbo type-check"}, "devDependencies": {"@turbo/gen": "^1.13.4", "@types/node": "^24.1.0", "prettier": "^3.0.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "turbo": "^2.5.5", "typescript": "^5.6.2"}, "engines": {"node": ">=20", "yarn": ">=4.0.0"}, "dependencies": {"@types/pg": "^8.15.5", "pg": "^8.16.3"}}