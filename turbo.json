{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["package.json", "turbo.json"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", ".medusa/**", "dist/**", "build/**"]}, "dev": {"cache": false, "persistent": true}, "start": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:unit": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:integration:http": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:integration:modules": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "type-check": {"dependsOn": ["^build"]}, "clean": {"cache": false}, "seed": {"dependsOn": ["build"]}}}