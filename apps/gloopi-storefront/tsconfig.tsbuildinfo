{"fileNames": ["../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es5.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2016.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2017.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2018.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2019.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2020.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2021.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2022.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2023.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2024.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.dom.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2017.date.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2021.string.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2022.array.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2022.error.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2022.object.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2022.string.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2022.regexp.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2023.array.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2023.collection.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2023.intl.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2024.arraybuffer.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2024.collection.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2024.object.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2024.promise.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2024.regexp.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2024.sharedmemory.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.es2024.string.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.array.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.collection.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.disposable.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.promise.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.decorators.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.iterator.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.float16.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.error.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.esnext.sharedmemory.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.decorators.d.ts", "../../../../.yarn/berry/cache/typescript-patch-384040965c-10c0.zip/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./next-env.d.ts", "./src/middleware.ts", "./src/lib/config.ts", "./src/lib/util/medusa-error.ts", "./src/lib/data/cookies.ts", "./src/lib/data/regions.ts", "./src/lib/data/cart.ts", "./src/lib/data/categories.ts", "./src/lib/data/collections.ts", "./src/lib/data/customer.ts", "./src/lib/data/fulfillment.ts", "./src/lib/data/onboarding.ts", "./src/lib/data/orders.ts", "./src/lib/data/payment.ts", "./src/modules/common/components/filter-radio-group/index.tsx", "./src/modules/store/components/refinement-list/sort-products/index.tsx", "./src/lib/util/sort-products.ts", "./src/lib/data/products.ts", "./src/lib/util/compare-addresses.ts", "./src/lib/util/env.ts", "./src/lib/util/get-precentage-diff.ts", "./src/lib/util/isEmpty.ts", "./src/lib/util/money.ts", "./src/lib/util/get-product-price.ts", "./src/lib/util/product.ts", "./src/lib/util/repeat.ts", "./src/types/global.ts", "./src/types/icon.ts", "./src/app/layout.tsx", "./src/app/not-found.tsx", "./src/modules/common/components/localized-client-link/index.tsx", "./src/modules/common/icons/chevron-down.tsx", "./src/modules/common/icons/medusa.tsx", "./src/modules/common/icons/nextjs.tsx", "./src/modules/layout/components/medusa-cta/index.tsx", "./src/app/[countryCode]/(checkout)/layout.tsx", "./src/modules/common/components/interactive-link/index.tsx", "./src/app/[countryCode]/(checkout)/not-found.tsx", "./src/modules/checkout/components/payment-wrapper/stripe-wrapper.tsx", "./src/modules/common/icons/ideal.tsx", "./src/modules/common/icons/bancontact.tsx", "./src/modules/common/icons/paypal.tsx", "./src/lib/constants.tsx", "./src/modules/checkout/components/payment-wrapper/index.tsx", "./src/modules/common/components/divider/index.tsx", "./src/modules/common/icons/spinner.tsx", "./src/modules/common/icons/eye.tsx", "./src/modules/common/icons/eye-off.tsx", "./src/modules/common/components/input/index.tsx", "./src/modules/common/components/native-select/index.tsx", "./src/modules/checkout/components/country-select/index.tsx", "./src/modules/checkout/components/billing_address/index.tsx", "./src/modules/checkout/components/error-message/index.tsx", "./src/modules/common/components/checkbox/index.tsx", "./src/modules/common/components/radio/index.tsx", "./src/modules/checkout/components/address-select/index.tsx", "./src/modules/checkout/components/shipping-address/index.tsx", "./src/modules/checkout/components/submit-button/index.tsx", "./src/modules/checkout/components/addresses/index.tsx", "./src/modules/skeletons/components/skeleton-card-details/index.tsx", "./src/modules/checkout/components/payment-test/index.tsx", "./src/modules/checkout/components/payment-container/index.tsx", "./src/modules/checkout/components/payment/index.tsx", "./src/modules/checkout/components/payment-button/index.tsx", "./src/modules/checkout/components/review/index.tsx", "./src/modules/checkout/components/shipping/index.tsx", "./src/modules/checkout/templates/checkout-form/index.tsx", "./src/modules/cart/components/cart-item-select/index.tsx", "./src/modules/common/components/delete-button/index.tsx", "./src/modules/common/components/line-item-options/index.tsx", "./src/modules/common/components/line-item-price/index.tsx", "./src/modules/common/components/line-item-unit-price/index.tsx", "./src/modules/common/icons/placeholder-image.tsx", "./src/modules/products/components/thumbnail/index.tsx", "./src/modules/cart/components/item/index.tsx", "./src/modules/skeletons/components/skeleton-line-item/index.tsx", "./src/modules/cart/templates/preview.tsx", "./src/modules/common/icons/trash.tsx", "./src/modules/checkout/components/discount-code/index.tsx", "./src/modules/common/components/cart-totals/index.tsx", "./src/modules/checkout/templates/checkout-summary/index.tsx", "./src/app/[countryCode]/(checkout)/checkout/page.tsx", "./src/modules/layout/components/cart-mismatch-banner/index.tsx", "./src/modules/layout/templates/footer/index.tsx", "./src/modules/layout/components/cart-dropdown/index.tsx", "./src/modules/layout/components/cart-button/index.tsx", "./src/lib/hooks/use-toggle-state.tsx", "./src/modules/layout/components/country-select/index.tsx", "./src/modules/layout/components/side-menu/index.tsx", "./src/modules/layout/templates/nav/index.tsx", "./src/modules/shipping/components/free-shipping-price-nudge/index.tsx", "./src/app/[countryCode]/(main)/layout.tsx", "./src/app/[countryCode]/(main)/not-found.tsx", "./src/modules/products/components/product-preview/price.tsx", "./src/modules/products/components/product-preview/index.tsx", "./src/modules/home/<USER>/featured-products/product-rail/index.tsx", "./src/modules/home/<USER>/featured-products/index.tsx", "./src/modules/home/<USER>/hero/index.tsx", "./src/app/[countryCode]/(main)/page.tsx", "./src/modules/common/icons/user.tsx", "./src/modules/common/icons/map-pin.tsx", "./src/modules/common/icons/package.tsx", "./src/modules/account/components/account-nav/index.tsx", "./src/modules/account/templates/account-layout.tsx", "./src/app/[countryCode]/(main)/account/layout.tsx", "./src/app/[countryCode]/(main)/account/loading.tsx", "./src/app/[countryCode]/(main)/account/@dashboard/loading.tsx", "./src/modules/account/components/overview/index.tsx", "./src/app/[countryCode]/(main)/account/@dashboard/page.tsx", "./src/lib/context/modal-context.tsx", "./src/modules/common/icons/x.tsx", "./src/modules/common/components/modal/index.tsx", "./src/modules/account/components/address-card/add-address.tsx", "./src/modules/account/components/address-card/edit-address-modal.tsx", "./src/modules/account/components/address-book/index.tsx", "./src/app/[countryCode]/(main)/account/@dashboard/addresses/page.tsx", "./src/modules/account/components/order-card/index.tsx", "./src/modules/account/components/order-overview/index.tsx", "./src/modules/account/components/transfer-request-form/index.tsx", "./src/app/[countryCode]/(main)/account/@dashboard/orders/page.tsx", "./src/modules/order/components/help/index.tsx", "./src/modules/order/components/item/index.tsx", "./src/modules/order/components/items/index.tsx", "./src/modules/order/components/order-details/index.tsx", "./src/modules/order/components/order-summary/index.tsx", "./src/modules/order/components/shipping-details/index.tsx", "./src/modules/order/templates/order-details-template.tsx", "./src/app/[countryCode]/(main)/account/@dashboard/orders/details/[id]/page.tsx", "./src/modules/account/components/account-info/index.tsx", "./src/modules/account/components/profile-phone/index.tsx", "./src/modules/account/components/profile-billing-address/index.tsx", "./src/modules/account/components/profile-email/index.tsx", "./src/modules/account/components/profile-name/index.tsx", "./src/modules/account/components/profile-password/index.tsx", "./src/app/[countryCode]/(main)/account/@dashboard/profile/page.tsx", "./src/modules/account/components/register/index.tsx", "./src/modules/account/components/login/index.tsx", "./src/modules/account/templates/login-template.tsx", "./src/app/[countryCode]/(main)/account/@login/page.tsx", "./src/modules/skeletons/components/skeleton-cart-item/index.tsx", "./src/modules/skeletons/components/skeleton-code-form/index.tsx", "./src/modules/skeletons/components/skeleton-button/index.tsx", "./src/modules/skeletons/components/skeleton-cart-totals/index.tsx", "./src/modules/skeletons/components/skeleton-order-summary/index.tsx", "./src/modules/skeletons/templates/skeleton-cart-page/index.tsx", "./src/app/[countryCode]/(main)/cart/loading.tsx", "./src/app/[countryCode]/(main)/cart/not-found.tsx", "./src/modules/cart/templates/items.tsx", "./src/modules/cart/templates/summary.tsx", "./src/modules/cart/components/empty-cart-message/index.tsx", "./src/modules/cart/components/sign-in-prompt/index.tsx", "./src/modules/cart/templates/index.tsx", "./src/app/[countryCode]/(main)/cart/page.tsx", "./src/modules/skeletons/components/skeleton-product-preview/index.tsx", "./src/modules/skeletons/templates/skeleton-product-grid/index.tsx", "./src/modules/store/components/refinement-list/index.tsx", "./src/modules/store/components/pagination/index.tsx", "./src/modules/store/templates/paginated-products.tsx", "./src/modules/categories/templates/index.tsx", "./src/app/[countryCode]/(main)/categories/[...category]/page.tsx", "./src/modules/collections/templates/index.tsx", "./src/app/[countryCode]/(main)/collections/[handle]/page.tsx", "./src/modules/skeletons/components/skeleton-order-confirmed-header/index.tsx", "./src/modules/skeletons/components/skeleton-order-information/index.tsx", "./src/modules/skeletons/components/skeleton-order-items/index.tsx", "./src/modules/skeletons/templates/skeleton-order-confirmed/index.tsx", "./src/app/[countryCode]/(main)/order/[id]/confirmed/loading.tsx", "./src/modules/order/components/onboarding-cta/index.tsx", "./src/modules/order/components/payment-details/index.tsx", "./src/modules/order/templates/order-completed-template.tsx", "./src/app/[countryCode]/(main)/order/[id]/confirmed/page.tsx", "./src/modules/order/components/transfer-actions/index.tsx", "./src/modules/order/components/transfer-image/index.tsx", "./src/app/[countryCode]/(main)/order/[id]/transfer/[token]/page.tsx", "./src/app/[countryCode]/(main)/order/[id]/transfer/[token]/accept/page.tsx", "./src/app/[countryCode]/(main)/order/[id]/transfer/[token]/decline/page.tsx", "./src/modules/products/components/image-gallery/index.tsx", "./src/lib/hooks/use-in-view.tsx", "./src/modules/products/components/product-actions/option-select.tsx", "./src/modules/products/components/product-price/index.tsx", "./src/modules/products/components/product-actions/mobile-actions.tsx", "./src/modules/products/components/product-actions/index.tsx", "./src/modules/products/components/product-onboarding-cta/index.tsx", "./src/modules/common/icons/back.tsx", "./src/modules/common/icons/fast-delivery.tsx", "./src/modules/common/icons/refresh.tsx", "./src/modules/products/components/product-tabs/accordion.tsx", "./src/modules/products/components/product-tabs/index.tsx", "./src/modules/products/components/related-products/index.tsx", "./src/modules/products/templates/product-info/index.tsx", "./src/modules/skeletons/templates/skeleton-related-products/index.tsx", "./src/modules/products/templates/product-actions-wrapper/index.tsx", "./src/modules/products/templates/index.tsx", "./src/app/[countryCode]/(main)/products/[handle]/page.tsx", "./src/modules/store/templates/index.tsx", "./src/app/[countryCode]/(main)/store/page.tsx", "./src/modules/layout/templates/index.tsx"], "fileIdsList": [[89, 92, 126, 149, 163], [113, 114, 117], [119], [88, 92, 197], [128], [95, 209], [95, 127, 200, 201], [92, 95, 190], [88, 92, 212, 213, 214, 215, 216], [220], [92, 186], [227], [89, 92, 234], [88, 90, 98, 241], [88, 91, 98, 243], [89, 92, 102, 165, 166, 172, 173], [248], [95, 252], [95, 255], [254, 255], [88, 91, 179, 180], [88, 100, 275], [98, 277], [102], [122, 123, 124], [85, 86, 87, 88], [85, 87], [85, 86, 87], [85, 87, 88, 98, 99], [103, 105], [104], [98], [169], [92, 113, 114, 182, 183, 184], [195, 196], [92, 131, 133, 140, 169, 194], [92, 128, 131, 133, 140, 169, 194], [92, 131, 135, 140, 220], [105, 113, 156], [113, 199], [105, 113, 114], [92, 131, 132, 211], [131, 211], [92, 131, 211], [92, 113, 131, 135, 140, 220], [95, 140], [119, 185], [218, 219], [114], [89, 113, 128, 135, 150, 151, 152, 153, 154, 156], [113], [127, 230, 231, 232, 233], [108, 157, 158], [113, 127, 161, 162], [98, 113, 119, 237, 238, 240], [101, 137], [89, 101, 127, 128, 134, 135, 139, 140], [131, 133], [132], [89, 105, 135, 140, 160], [89, 125, 135], [121, 125, 137, 142, 143], [121, 125], [89, 125, 127, 135, 144], [146], [131, 133, 136, 138], [89, 93, 105, 127, 135, 137], [93, 96, 141, 145, 147, 148], [127, 159, 161, 162], [98, 237, 238, 240], [105], [89], [129, 130], [192, 193], [110], [178], [100, 119, 177], [89, 167], [105, 113, 151, 152, 153, 156], [92], [89, 169], [115, 116], [113, 170], [90, 91, 113, 117], [166, 172], [88, 113, 168, 171], [152, 153, 154, 156], [108, 127, 158, 204], [94], [105, 125, 127], [105, 127], [95], [162, 203, 205, 206, 208, 250, 251], [113, 203, 205, 206, 207, 208], [89, 127, 260, 261, 262, 263], [106, 107, 114, 169, 193, 261], [100, 106, 113, 156, 176], [109], [106], [266, 267, 268, 269], [88, 100, 177], [155], [259, 264, 265, 270, 271, 272, 273, 274], [100, 264], [105, 109, 113], [225], [224, 225], [108, 222, 223, 226], [245, 246, 247], [108, 236], [97], [88, 98, 100, 177, 239]], "fileInfos": [{"version": "c430d44666289dae81f30fa7b2edebf186ecc91a2d4c71266ea6ae76388792e1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "impliedFormat": 1}, {"version": "ee7bad0c15b58988daa84371e0b89d313b762ab83cb5b31b8a2d1162e8eb41c2", "impliedFormat": 1}, {"version": "27bdc30a0e32783366a5abeda841bc22757c1797de8681bbe81fbc735eeb1c10", "impliedFormat": 1}, {"version": "8fd575e12870e9944c7e1d62e1f5a73fcf23dd8d3a321f2a2c74c20d022283fe", "impliedFormat": 1}, {"version": "2ab096661c711e4a81cc464fa1e6feb929a54f5340b46b0a07ac6bbf857471f0", "impliedFormat": 1}, {"version": "080941d9f9ff9307f7e27a83bcd888b7c8270716c39af943532438932ec1d0b9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2e80ee7a49e8ac312cc11b77f1475804bee36b3b2bc896bead8b6e1266befb43", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fb0f136d372979348d59b3f5020b4cdb81b5504192b1cacff5d1fbba29378aa1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a680117f487a4d2f30ea46f1b4b7f58bef1480456e18ba53ee85c2746eeca012", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "959d36cddf5e7d572a65045b876f2956c973a586da58e5d26cde519184fd9b8a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "965f36eae237dd74e6cca203a43e9ca801ce38824ead814728a2807b1910117d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3925a6c820dcb1a06506c90b1577db1fdbf7705d65b62b99dce4be75c637e26b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a3d63ef2b853447ec4f749d3f368ce642264246e02911fcb1590d8c161b8005", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8cdf8847677ac7d20486e54dd3fcf09eda95812ac8ace44b4418da1bbbab6eb8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b4b67b1a91182421f5df999988c690f14d813b9850b40acd06ed44691f6727ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "df83c2a6c73228b625b0beb6669c7ee2a09c914637e2d35170723ad49c0f5cd4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "436aaf437562f276ec2ddbee2f2cdedac7664c1e4c1d2c36839ddd582eeb3d0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e3c06ea092138bf9fa5e874a1fdbc9d54805d074bee1de31b99a11e2fec239d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "87dc0f382502f5bbce5129bdc0aea21e19a3abbc19259e0b43ae038a9fc4e326", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b1cb28af0c891c8c96b2d6b7be76bd394fddcfdb4709a20ba05a7c1605eea0f9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2fef54945a13095fdb9b84f705f2b5994597640c46afeb2ce78352fab4cb3279", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac77cb3e8c6d3565793eb90a8373ee8033146315a3dbead3bde8db5eaf5e5ec6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "56e4ed5aab5f5920980066a9409bfaf53e6d21d3f8d020c17e4de584d29600ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ece9f17b3866cc077099c73f4983bddbcb1dc7ddb943227f1ec070f529dedd1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a6282c8827e4b9a95f4bf4f5c205673ada31b982f50572d27103df8ceb8013c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1c9319a09485199c1f7b0498f2988d6d2249793ef67edda49d1e584746be9032", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e3a2a0cee0f03ffdde24d89660eba2685bfbdeae955a6c67e8c4c9fd28928eeb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "51ad4c928303041605b4d7ae32e0c1ee387d43a24cd6f1ebf4a2699e1076d4fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "60037901da1a425516449b9a20073aa03386cce92f7a1fd902d7602be3a7c2e9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d4b1d2c51d058fc21ec2629fff7a76249dec2e36e12960ea056e3ef89174080f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "22adec94ef7047a6c9d1af3cb96be87a335908bf9ef386ae9fd50eeb37f44c47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4245fee526a7d1754529d19227ecbf3be066ff79ebb6a380d78e41648f2f224d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "73f78680d4c08509933daf80947902f6ff41b6230f94dd002ae372620adb0f60", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c5239f5c01bcfa9cd32f37c496cf19c61d69d37e48be9de612b541aac915805b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, "f2b3bca04d1bfe583daae1e1f798c92ec24bb6693bd88d0a09ba6802dee362a8", "7a3399181fe7f9e8114a5d901b8a3b24c989ee052ce58f88c0b1a70850d64174", "2db4d8e2fa2258e77a32c35cfc0c2e1e2ee7b1c01e98b2a2125c3b879b55f61c", "0a5486693d2e2bf6bacb5e5f010a94947faf0579b7d7004d66e0ab922fd27bb7", "96fb4c0be7de4cd8390dc1f578b90b9f041f3f5f394ebefa62c153166878848c", "6f214f558005dd7aa507ea0519c4dc876b040c9e30d8619fa8c0796f24a37115", "8ee9278631bec547ddb7286941fb8a930e045e157290cf9c425f5e32926a612c", "83ad3a07301a019f8cda53233a0902b0ad04743d5f206ab4dd242750aa63ede7", "62ab4b9cfdf88f30e4e26e12c6b990330955db6b890a2efaf02624695c09fb80", "5f7f5b1e6e8dbc8dc9a4546af42002a1baeaae94a5514f2896521a6ad5f37621", "dba07468c1c6a8c8a561973b43dea1120bab1ffe4e10b0ad5b996df0a21a007d", "611c4d6d5ee753d0897921190e23201028941a4308c060e0013cbd54e20cff22", "5c0ef57e87de0a459a25de906502d548749147488fa02ecaf454e61a158cd899", "3fad5b84fd9d23968d47981e435eec6c6e9264f529907eb97cdc97ea67cd5b15", "4016b39e5437a4af3f28b11cb892606178f14b3886dda504bddda0c5026675e7", "b6cd84d49db681165824c078edc4e3b5862c9f0e60a614c76c5bb2ca92786c70", "0360786ef43dd5c120c168b6db80d0566cb1b8296a5e6a0e85d0376c4c8d69f0", "152d6454329c847334779e3be395dea8667089427ebd5d7268b555d9d1d22072", "e42006aadc9d2147ac90a08f65c1743d51abd36ddc09b9408f7059e546c92c98", "ce62606289ca23e7f9ac14020eddc53d96b7cdbdeded2e456f041671942db7b9", "960ffd40fdbd0c26b1d67207129c2f13b2ae24be7b3d894ae739a9f38871e9c3", "22128e3c56eaf8fa436664e1f8e8130ad5f7c227083878407d9401332977e460", "0e40c7d3cc46d2141cc25da5e0a242f734df06d5e5a49dd06e010376cfa3da86", "d56f47b2589aff28b556eaf73a9f38bd061e6f9592659d16215f704d34738b63", "012c9270a835420c01875933075b531dbbb6e626764567613e39300871a6eabe", "86a1ca6c43798dc6573bd6b6d575ba4681a293ad5bac56261ca68313eddc7012", "1d39d43c4654ff873da34002118c0d2deb62bde8a9eea016562d1f3ef5646776", "b079b22c0df9613267fa653099e4ae8a09207c70dc50d08781ab9c9d2939e04a", "2fce8d05733ddf8c068c17b733689b0847dbf76221664ddda560f43f8e9f1cbe", "2e81c6d3c2feff2a7e2fdd8ccc35a937d94971eb4dcf7c6ebda47728962b70cb", "962a482a53cc7465d865d7122961cfc8ef310d700a40396503c24a5b9b1032c5", "203c09a2b36ee478ff8a41ce9cb3590e6fb325e4f96640b6f8e44f06deff6e53", "1de5a4d15654509f4172cf1679286ace90aab73c6753edc41b7c560550038d42", "d652c5974a73c173835050680c2af0f236b98c59b8759d93a33091aac281191c", "1bb181ac1d6e7b8ed2846726c67fe77bc312ed9c812a3c721a89bb7dd802e949", "24575e381ff100452b4db0632f37f99092d31825be70ada05fc993fa0abb4712", "ca102707ed6cf98504bbb2cc1c67affbdf09933c7ccabc9b815a16bb002ad28f", "0ec5816d08d629055b9d26d9f8d04ef4e07a96d3757c0fc83a1784ba58c786cd", "b634f0bf8138679bbda42e99b30e41c28b645830577c9791e93caaea80f9c313", "872960846b9c066e0e57d8d854a6d86c69531c172cdae91d6e1d343cc533aaa2", "836790e2b259e5a987cae53daf58f167cbf929bbfecb987dca4ba187b8aa9e4d", "08274e2e2a97f4f3a6810e1a9a5b45776690a56012ad7b477d0891855934d445", "39ead1b310096a655c7d340be9abf4c84bead2621299cc09b4f01d290c524c10", "f4c2d426fff6303c56ff47a3e3039a0392a5a9d82e7f02ab27872db9306d7f0a", "f4f295885ed6a2540081cc29887291806cbd8fc0b3a6e08468a1c7fbb076542a", "0f76a0a2e8210c41806422ce114ecf23e571d396e15bd6e9f0ca0eb916bad97c", "b3fe7faf60af62928305b7a60084127491a69217f24f143c20552ba2795b8713", "d0ff7da91ac525bbfa5cf2d58d3caf779e7df94d22c0773b4ebfec792b26aace", "271668fbb9df52328245a1e3e0ab7e3f100f3ce60bc9c169128fffdf972e91c6", "bee0f24e6f27349b627af0699858513a026152d676cd1a88e8c9d916c8e9e4cf", "efcef1cc1d700e5274c017f81ce276064b8e491ce93657900892efb8bd509b34", "9723a57f9245f71627e908892ea398959a958c44342277a34a3468591d60738a", "95932abc5f783bd81be6e1313a1d25dcabc932ac323c5cd7b085d0113dbfa478", "5481b6127f0c6ba02cd5c6349a5a306db0cad721d2c34c9bd460a71bce6f6e8c", "91c5b779d6e97d9a1c6578632d037085778a5700f09f8224ab0f6b9d9e82b2db", "df22c26f826b10bc66db3ec3bc0c673d0647a38e73c25f918e609c1ebebe049f", "8281392e7754318c4cfb510a38537b865a6215ed412c1cc4d6cf36461e1f968f", "07c344af3451d6008d1f3055966bc2718e363ea4a91840e78c6f9d531ada6add", "68727678f15fc58799701e6d6a3cf939cce709fa3cefa19e5fdd2cc207402465", "a4e83f9a4abd2973069e91ec320c1f3cc5d7a84f1802aac7010930e9842f3287", "425c5b2f7cee88afff8494429313e9445a138bd59335fe702b2ab9bb2a04cb43", "cc402207b1ad0447fe29b81633fe87078dbf76cd72a1cb5f0a3440d407b61831", "a42183a39af6f6e25f43b1991a53e62d83c4d7ea433b55a6414a5edbc2164518", "fe401751fe8da263e30b117d805c2c2046551e8d977acaaaf777e9e51e5fa02f", "4b525653cf9bfe32bb2e53ff9c1ffff3014559c9e78dcb0ebda9d18eb2621eaa", "ccdb2d5c8d1fde4b5f1e926aba457c1b57976ac303eff25bb1bf15d3122c27de", "a734a0367c9a4acba4181b1ca6f9d3245ef7e17f442835c1a94d1cf0116a19f6", "cfc10b2a9708e5e5999c1cb82e49653e4f83131e14d3d1e0aa9a5738eddf97ff", "144410d14eae513d1535c362f1fcc6e9e5ca634506b4452a32d977a49042b6e4", "13c78a95fda28572824c2108ab9b2c33af19e0d763e0c181337a77ac838d3f50", "8dd703ef55674459dae800bf443e2a0b38dbb35d39be7f79fa56ebc10753c64f", "c92bbc1d39c2c7879b2abfa33adedb5f5a44316ac208bbc1af237567b228ef56", "bb27d75136aeb41971bc9b9d86f321a9240feaf58e9f53760434e5d1f6e644ee", "114c8b74fe63f3e9ee3d9053163a66ebf64aeaa8acde1dfc60dd64dbb2b6df95", "3045766bb1253af5f10e6a60db05236fddee30e2890522a4ef1ae119987fa212", "6db5d20dc3b4bd3765b2412a952b8484dd747d6dde7d61e8ca88583827c4688b", "dd223a962b9a6cc56e2d4fa7362874156fcfc773a2dfc981157d48d35d2c30db", "897be48cac33474d21e5419edce00de20d0fc8e3c0dc2afd6c1aea296178d45f", "5ede408a70ad4f64ef5c7a588c903136a7def10a26e0d401c335eaaf5940cfb3", "07ff49de4666f135bd1dc35c3d80fb19ec22f9a78e6bb0fb6882d6a70c61445f", "d7d3c266bdc62f100215f0a81e3ee87c89c12419079f5a5b2c602f5a83c6e6de", "216176a117823bba3f209f00297b02d08fabde07da58328994e15ffb1500d9a6", "652044cd41692497acca161838cfb784ba6afe6275c5f3693234831880d62204", "0bad2028b25abe31c235be825fbfac6e9943ba8d3ff664fa80ffdfbfde1e9c70", "751dc2e2619fb9c6e587bf9760ece77178f8653f3daebfe711614130eb82f643", "93aecbe24c8e2dd06005a22770b0b28423e47527a47c98de2d53fe14b1df4440", "a837b72798d32746f2bc2aa529821aa3c1a74106f7e3c0911700d4fc69fecaa0", "9b21ee87947bf969af8062ded1ce4d875496243a44ca04e4e14b38e612cc49ae", "8bf3c7af99e71dfa4fcb633b375f34b793d315611c67d09a14d1adfe2df6b603", "1d2e931a3bb7665dfcc3771bfbc6008b410507f05f720e4eee995e3632d072ef", "e5b6ee123470ce1346001a94262083bee8e389e80c29de018e7a35b266e74be0", "e81146d774f3c051bc467bf043bb05abba06fa2dff4ab3d8e1e96b80070382de", "b4df486d1a8016386d242a4967f7b314fb187304dc4035ce76ff472c0c7f9ba9", "25660e03d7a6ad719b7b21a7491d8ac8053d58738c6b41df854e33c48bf84900", "f203b2801f11df5222e72869712fca561959a2e4638a1b4af873fb237af307ca", "b2358a3f069ba65faddf1bc58d5c722c0c94b53285bd871c84093849d55f1301", "3af775ed07cca0dd80feadc8428e9c9e11ea44b36bd9641440c2609fa4d5a95a", "4aa5b189b6fc6cb716296bfe59a33d09863623caae3962a46ed800ba3ba633c4", "3155b18ce7f8de347b60728ce21fc7cbd67260256cfa30dcb5c9f25a0a277c87", "98f249e3f066c85444a2ce749aa63ec31686f107371342ad0f7115dbffc1b28b", "45197672b7c0a047b10a90d70f4bd031cc451ef7853ae4615526b4faaf26b071", "562b3d4c00033e15705ee8ef91f1cc22ebb9c09c7c1dda892370fdc7389b33d0", "2459029296e4f52acc789bf5a0348e4ceafc4db5b1e3b70d28c15b92aa4ea176", "5f3fe5a496e2f431630260340d08ffedca1c947b12fc49ce98a719278eb0dc31", "42908d4ffd6a18b1fc821fd9c0284fc093ac5b939cd678bb496bcfe2977f0b55", "311f291629a52d021c82f51e040f3b99077742cfd65064a0aa35dc01509a07bc", "311f291629a52d021c82f51e040f3b99077742cfd65064a0aa35dc01509a07bc", "d6cb120ddcfcc86d0e820bdb594567dc12298f4047400111783a07f26ea16743", "01c1fcfe53e201c8121056187699c7c65ab914ae329b8e8d733cdd6b83faa529", "f053cc93dbc9c09a53f7f0231b2dfffed10cf8945010dff9e1f33cae6972fda7", "9f368b2e41421c011cd50396cacd3c17077d2dc32ab644bb30342a45be118b60", "4ec2af5bc26f589fbff3b8a68675dd9cd8abc38a920cd96d954691228964d5af", "f30325169af635779730a83b4bbd5fbdcb998de16b9f16cecdaaa3db36330e50", "6775496d89a552205c904a93f42ef51b41cbbf7d66989e252f580acec628dcfe", "58eea0bc8b0bf7634477a9ec29cd919931d844becdcb33c7f568535fd1ca0496", "df5e2331ca960cbc8a69c8f7519c86574f63af69a6e3d62b8f501e004300e756", "663917b1429c34cedb488ec4570c174a2ccf9064a7673f5dd8874c9fd3f7009e", "9ac02e82d3a270e9ff54e415e2f0ecc9cf8377a27652d93525b9c06b6970d7f0", "03033ce14a6ef39d608e8870e50bd18430bb81752176bf1392657ef13a784ea3", "29863c4e7f9feae8e9dbb53c04662482cbd4da4f5c2654fedd40b8ec53c69537", "f61ea2d8797aedf79eddd07f6a1a9ae37f9d7144f443da5fdf3f15e2bb60dfd9", "98fe80d8fde11792e04c37c7d7bf89b49fec94acfef7fece0ccee1a73666fe9d", "f25d4d62908dde3497097ba169bd9e85c32598ccda4b5845c5330e90547d0848", "c10666cec7f572ce79b439533dca74a29286d51f6dcc9192faf94181b26e070b", "5400463af6ca15b35aa9e185f0976e0891c589e7db50068c3c17514fba65ea8c", "9e64b6cba732585ebefa215699c01bc98bcc774589b36cdd72ded7c405019946", "a291cc8cd8dc5e51539cf58af52761d70ba39ab7f22f0904b740803ea1f2eb6c", "ec3ca34ea931eadfead1de4099d9d5ca6566f1548a006da602bc7686added801", "8c54bf01dcaea6a71fd8ef55c5b5af0fa79c55d2857a9f4a47cb13b4f7ddfd1e", "eec021a0a0916dce5ad16a99ebf24b96faa349e2eaaa5b3df74a54bf621378f7", "7387d8355a060e7b4b20c3fbd0b45f08a3f6761db8176d8dad6494e18632699c", "c490471f102423634f263e738cd89a0ea52a0683b92ea55090cd00d2debb670f", "52fd8222ebe9aacb0935dd83ea82d7201e74ffe64c70da62e5a471ab14932ffd", "04e8d77c6f94c0ae6f5c0e843c18cb8364543bd8922c94fd29dddd5547b8129d", "d7fe1c68d5879efd9fca465fcbf242c4e75c3158f87b2773d62eb3b281b2113e", "b29468b49d6a5dbc8545a06a8d9ada828792a9888c95dee2b818c8a18788b15f", "64e327c4f6bf771965ada789fcb205064ce48b9a0f3e7f8fe03b93940812726d", "e1a0e16d9ab40b2a47e40bcc6b9781b6f42db3ed08980dd633fb9dbe29cc9d96", "4ec8277ad9303fdb453eeb169f8745b1809fe878d7c78285f3d25e8f98510ef5", "8431e23b99f3030c06c5f7323e0ad8e550526416addde408419532fb42577373", "46a4dd25d4e06f4488a5f6923883e297a2764d84d07068277ac1f561649813a4", "4d8655319f38248b7df582d83d2aae4058e7e55e6cfbe014b1226dc89ed63ec0", "257a846008e11744dc3ab5600a1dd5ec3ec6cd01b92d6055bf4c07ae21c7cedf", "79d7b09784dcde8faa1da3495e0b224e18c221f5e6079e6c0e48c89c31c58823", "3e83d3fcfb1470d8a691c22efbb5efc51ab4d3c5170d235bbd7a72e8b3ccbf48", "13c3966ea6b1c7564d64b0e1f416bede2a34f939a3b7663b01b5b957a79af035", "97f46df7675d6bcc4486c2dd1e7cfb4a0ab8c0b66e47701894bccce02011dd18", "707c3ffd3b192fd880117226c3d942b10c7894dfa9725fb53731717f06284e18", "6f73f11a3b0bcd4fc941244fc7b990f4102f94b7595787bae3e626f348fa165d", "526f06732e939de71bf4f4244d103e8ef452bf5d2b33f081213d1c788f05112f", "46936c3af9d2cb5c12b838c353e4dff552de843eac3d4dde56ace44fa9801bed", "84f5bda13341f42a444535ef000311f5c13a7a946d04a6adda6619e004c40e89", "bf633e09ddb576dae0e071f18a8639b62abb09df075726bf5f494f498e5e7e86", "c7bbc170a866d5493491c2c16c26e02f269e37e00feff31ad6e6f3c47e365cff", "b5a147636f6a940b2f6cb04676dbc11fb2833287f9f361f31680930c0dd7e19e", "2cf14fa25ae1264529063c6d98c5902afa2b87ae95f6634b9615dc9368bff7a1", "6965ae55e0c61ed0dba0e40019011ca8149f2c653ebcdab455602e1454410dbc", "f5ce3c88cade58f183c5d193e551dcfda9820c95b333f6203d35dfa491c4f8dc", "98e5bafb414301fa83667440432be20e1b990559a3344f432ea091047b9abd11", "a3dc0dd4d71dde58fe47dbb256aaa6497ab4fa370b4c9f99f65fbc90749a9431", "75d3c071aa0c04939aa01a9fe7f34ab7d25aeaed5a91386c64edecb5f03c601f", "fb09d7a22d393974510bc910a11a71476b64960ff751f4283d2e86930a5a2068", "4cddf3dfc5a652cc6b596ed9856b849652c86cd64552934e5c79a9a5f3760f1a", "7b863c23e5e61df72472834d27e861dfd553e69b557690aa446ed7c181915482", "fec539dd92743b853fdf98bf820219305e4e9feda31c61bf4a53eedef6ecdf8c", "b2c5b7576b64924570f19db57e860685b3ec10c88f207c1cf3960d696e993ddd", "ae618e13f0881d87c981270aa9d85cd0d9b88aa6339e514a22bf01f9363d4311", "fcb482b00bc99b25ddfe9227e1295a1120ce9d3274d6325b7e5c251abfbac2d5", "f4963196ebd0f40ff4831a58225e974a9fa5bda734525469553496461187ea47", "a6fd781899a708a4bdaea20250a05b2841b47d1997204c60424914181ab3f097", "e0c34835b11014ea67d9842b3db89024309e916aa2b4c8b37bbcac134ba03d0b", "e3220667f29011a6e118e40985f32aa2e5f55a8e0f458f922fb44f006016179a", "8610dd80cde5b2161f7e05b3fc7d61923fccd561cd5701f6d3eb47ff96bac6a9", "b52593455d307e69b97f1013bad6d423bb6db26f465eb1b4321de74fdd3599b9", "a4ef3ea365891e9391309f8c9c485b60e81d1b591b056aae17f8e617095e8295", "02bbfe08592ac71895c545f549189de05c4181424751fe2f2565ab2d855512d2", "58b797e1154f55035b2bc65ff7ce1da6fd1d3e74645d112436981bce4c006cba", "e4fba167abc17633d4c0494fccd30b2a399aabf8d3171b14e43e2cfc108c141f", "28118b73f0755f2e99b2a7ba6e4c81dc0b8356b46bf7662049af32670c48befa", "b132acc1709709d547739d3b0200d994fbf42ecd74618c95c8925ff9ff505ff5", "73fa3c90c98e51d4abde14e7fe0dac58fdee1d49a3a910646a78658d139888af", "207de96869c2c61f22c871455e4de904f80cddc0e3dc440a6b7055def0d43d9d", "a37dddbc0f712b7d8e91aa412e3ac51489687bf6f85a6c692db9fe54e6167bae", "ac368c517c39bc267c6c2748474c9d8f5733b9b464c259fe5b66b49c90c52bd7", "98408f68f822d5e7ab1e2c2d78d1624a518c72e3d663cd6325571e34ea40eec4", "ce58f819f913265248b43bd404a6f0209e22d12d6ee529cf502fd165a8c2feec", "912aa08a319e0516d080d15599ca87918cb3b64346ba59f511ed12cb8f8ae454", "e0b2fc6c2cdb26c816890adbd56fd775ab66548a5547410790e49d50c4121e28", "b7fa3d6dbca910d636f3d766921e8c367bcfccbf5d65d8bd3c8ba4435e59738d", "23d7b738fa96d299fabaf1000e2b021a5f1698ac4b7b53e9e8743fc8c889540e", "00f10a0a560592482453a86a346e1e9b2f8e081bc71fe73c7501f76b9cf373ae", "5d99c94523104e64fe26caba77b703e9122c3d19c08df23c3fa3b1b709c9ad3e", "33e9b902945b7fca63eff402539b5116962f46ad33e20cc58c46c538dceed584", "c3d1ad1d1cc3777f9f2439e16a4aba8388e02b8a0eae20a387ef085b5850eee3", "c4ea7ce057998618b13ec3bfc5d078233fa6e57a31b2fa5c789db1ce64d1dca1", "6e719b44a788760f8c3cdbebf3604f2f461f88d76688af61b939ce62fa1c48f4", "ca6e7c842c988c1aec6a6fdbf9e9a509f65d889a0469ce8c0490b11eb13e85e0"], "root": [[83, 279]], "options": {"allowJs": true, "esModuleInterop": true, "jsx": 1, "module": 99, "skipLibCheck": true, "strict": true, "target": 1}, "referencedMap": [[164, 1], [118, 2], [120, 3], [198, 4], [189, 5], [210, 6], [202, 7], [191, 8], [217, 9], [221, 10], [187, 11], [188, 5], [228, 12], [229, 3], [235, 13], [242, 14], [244, 15], [174, 16], [175, 3], [249, 17], [253, 18], [257, 19], [258, 19], [256, 20], [181, 21], [276, 22], [278, 23], [111, 24], [125, 25], [89, 26], [90, 27], [91, 27], [92, 28], [93, 27], [95, 28], [96, 27], [100, 29], [88, 28], [106, 30], [105, 31], [99, 32], [211, 33], [185, 34], [197, 35], [195, 36], [196, 37], [219, 38], [199, 39], [200, 40], [190, 41], [213, 42], [214, 43], [215, 44], [216, 43], [212, 44], [218, 45], [201, 46], [186, 47], [220, 48], [150, 49], [232, 3], [157, 50], [233, 51], [234, 52], [230, 53], [159, 53], [231, 54], [241, 55], [138, 56], [141, 57], [134, 58], [133, 59], [161, 60], [146, 61], [144, 62], [126, 63], [145, 64], [147, 65], [139, 66], [148, 67], [149, 68], [163, 69], [243, 70], [162, 71], [151, 72], [131, 73], [119, 51], [153, 30], [154, 71], [194, 74], [266, 75], [123, 75], [114, 75], [130, 75], [129, 75], [267, 75], [122, 75], [183, 75], [115, 75], [116, 75], [184, 75], [155, 75], [268, 75], [128, 75], [160, 75], [182, 75], [193, 75], [179, 76], [178, 77], [168, 78], [167, 79], [165, 80], [170, 81], [117, 82], [171, 83], [166, 84], [279, 85], [172, 86], [203, 51], [204, 87], [205, 88], [250, 89], [207, 71], [251, 90], [208, 91], [254, 92], [252, 93], [209, 94], [264, 95], [263, 96], [177, 97], [176, 98], [262, 99], [270, 100], [271, 101], [156, 102], [275, 103], [274, 104], [272, 51], [173, 105], [246, 106], [226, 107], [227, 108], [248, 109], [237, 110], [273, 110], [238, 32], [98, 111], [277, 70], [240, 112]], "semanticDiagnosticsPerFile": [[84, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 86, "length": 13, "messageText": "Cannot find module 'next/server' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 121, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 180, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 250, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 1192, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'next' does not exist in type 'RequestInit'."}, {"start": 1753, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2806, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [85, [{"start": 19, "length": 18, "messageText": "Cannot find module '@medusajs/js-sdk' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 140, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 197, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 303, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 361, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [87, [{"start": 60, "length": 14, "messageText": "Cannot find module 'next/headers' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1168, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 1704, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [88, [{"start": 123, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 462, "length": 7, "messageText": "Binding element 'regions' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 820, "length": 6, "messageText": "Binding element 'region' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1186, "length": 6, "messageText": "Parameter 'region' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1232, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [89, [{"start": 123, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 171, "length": 12, "messageText": "Cannot find module 'next/cache' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 209, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1239, "length": 4, "messageText": "Binding element 'cart' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 2427, "length": 4, "messageText": "Binding element 'cart' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 5590, "length": 4, "messageText": "Parameter 'resp' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10226, "length": 7, "messageText": "Parameter 'cartRes' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [90, [{"start": 60, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 683, "length": 18, "messageText": "Binding element 'product_categories' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1207, "length": 18, "messageText": "Binding element 'product_categories' implicitly has an 'any' type.", "category": 1, "code": 7031}]], [91, [{"start": 74, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 452, "length": 10, "messageText": "Binding element 'collection' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1039, "length": 11, "messageText": "Binding element 'collections' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1480, "length": 11, "messageText": "Binding element 'collections' implicitly has an 'any' type.", "category": 1, "code": 7031}]], [92, [{"start": 123, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 171, "length": 12, "messageText": "Cannot find module 'next/cache' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 209, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 938, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1219, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 2762, "length": 5, "messageText": "Parameter 'token' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4770, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 4950, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5423, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6764, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [93, [{"start": 74, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 743, "length": 16, "messageText": "Binding element 'shipping_options' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1420, "length": 15, "messageText": "Binding element 'shipping_option' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1471, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [94, [{"start": 52, "length": 14, "messageText": "Cannot find module 'next/headers' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 92, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}]], [95, [{"start": 183, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 695, "length": 5, "messageText": "Binding element 'order' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 726, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1351, "length": 6, "messageText": "Binding element 'orders' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1384, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2015, "length": 5, "messageText": "Binding element 'order' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 2080, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2356, "length": 5, "messageText": "Binding element 'order' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 2421, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2699, "length": 5, "messageText": "Binding element 'order' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 2764, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [96, [{"start": 134, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 623, "length": 17, "messageText": "Binding element 'payment_providers' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 677, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 680, "length": 1, "messageText": "Parameter 'b' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [97, [{"start": 33, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 96, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 440, "length": 47, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 679, "length": 163, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1518, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1561, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [99, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 729, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'variants' does not exist on type 'MinPricedProduct'."}, {"start": 749, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'variants' does not exist on type 'MinPricedProduct'."}, {"start": 831, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'variants' does not exist on type 'MinPricedProduct'."}, {"start": 858, "length": 7, "messageText": "Parameter 'variant' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1320, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'created_at' does not exist on type 'MinPricedProduct'."}, {"start": 1356, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'created_at' does not exist on type 'MinPricedProduct'."}]], [100, [{"start": 129, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1887, "length": 8, "messageText": "Binding element 'products' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1897, "length": 5, "messageText": "Binding element 'count' implicitly has an 'any' type.", "category": 1, "code": 7031}]], [101, [{"start": 30, "length": 8, "messageText": "Cannot find module 'lodash' or its corresponding type declarations.", "category": 1, "code": 2307}]], [102, [{"start": 43, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}]], [106, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1821, "length": 1, "messageText": "Parameter 'v' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [107, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}]], [109, [{"start": 27, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}]], [110, [{"start": 72, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}]], [111, [{"start": 68, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 236, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"start": 272, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 313, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 328, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 371, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 385, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 397, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [112, [{"start": 33, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 72, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 112, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 136, "length": 11, "messageText": "Cannot find module 'next/link' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 295, "length": 90, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 392, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 452, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 464, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 574, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 904, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [113, [{"start": 31, "length": 11, "messageText": "Cannot find module 'next/link' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 69, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 105, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}]], [114, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 191, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 354, "length": 151, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 510, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [115, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 181, "length": 152, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 340, "length": 635, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 980, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [116, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 181, "length": 152, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 340, "length": 1991, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2336, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [117, [{"start": 21, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 270, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 406, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 425, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 528, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [118, [{"start": 277, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"start": 313, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 381, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 431, "length": 76, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 799, "length": 101, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 949, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 969, "length": 100, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1101, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1409, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1452, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1465, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1478, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1547, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1560, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1651, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1662, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [119, [{"start": 33, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 72, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 205, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}]], [120, [{"start": 99, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 259, "length": 90, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 356, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 416, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 428, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 538, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 613, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [121, [{"start": 60, "length": 19, "messageText": "Cannot find module '@stripe/stripe-js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 105, "length": 25, "messageText": "Cannot find module '@stripe/react-stripe-js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 157, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 205, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 370, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"start": 461, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"start": 497, "length": 14, "messageText": "Binding element 'paymentSession' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 515, "length": 9, "messageText": "Binding element 'stripeKey' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 528, "length": 13, "messageText": "Binding element 'stripePromise' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 545, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}]], [122, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 185, "length": 174, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 366, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 383, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 398, "length": 779, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1182, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [123, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 185, "length": 174, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 366, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 388, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 403, "length": 432, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 840, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [124, [{"start": 38, "length": 134, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 179, "length": 248, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 427, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 441, "length": 272, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 713, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 725, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [125, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 53, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}]], [126, [{"start": 41, "length": 19, "messageText": "Cannot find module '@stripe/stripe-js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 79, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 158, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 325, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 481, "length": 4, "messageText": "Binding element 'cart' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 487, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 583, "length": 1, "messageText": "Parameter 's' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 927, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 942, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [127, [{"start": 20, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 99, "length": 85, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [128, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 187, "length": 187, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 381, "length": 140, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 521, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 537, "length": 189, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 726, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 738, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [129, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 183, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 346, "length": 311, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 664, "length": 340, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1009, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [130, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 186, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 349, "length": 828, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1184, "length": 155, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1344, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [131, [{"start": 22, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 101, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 505, "length": 4, "messageText": "Binding element 'type' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 511, "length": 4, "messageText": "Binding element 'name' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 517, "length": 5, "messageText": "Binding element 'label' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 524, "length": 7, "messageText": "Binding element 'touched' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 533, "length": 8, "messageText": "Binding element 'required' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 543, "length": 8, "messageText": "Binding element 'topLabel' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 565, "length": 3, "messageText": "Parameter 'ref' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1051, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1209, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1281, "length": 419, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1711, "length": 241, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1998, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2031, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2050, "length": 8, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2107, "length": 264, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2436, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2467, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2480, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [132, [{"start": 30, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 68, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 195, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 485, "length": 12, "messageText": "Binding element 'defaultValue' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 499, "length": 9, "messageText": "Binding element 'className' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 510, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 536, "length": 3, "messageText": "Parameter 'ref' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1014, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1028, "length": 398, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1437, "length": 240, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1690, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1757, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1800, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1820, "length": 84, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1945, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1961, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1974, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [133, [{"start": 65, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 194, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 361, "length": 6, "messageText": "Binding element 'region' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 369, "length": 12, "messageText": "Binding element 'defaultValue' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 395, "length": 3, "messageText": "Parameter 'ref' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 690, "length": 7, "messageText": "Parameter 'country' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 961, "length": 5, "messageText": "Binding element 'value' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 968, "length": 5, "messageText": "Binding element 'label' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 977, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 997, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1058, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [134, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 129, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1202, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3725, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [135, [{"start": 168, "length": 80, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 255, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 268, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 280, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [136, [{"start": 32, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 65, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 275, "length": 8, "messageText": "Binding element 'onChange' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 287, "length": 5, "messageText": "Binding element 'label' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 296, "length": 4, "messageText": "Binding element 'name' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 319, "length": 10, "messageText": "Binding element 'dataTestId' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 353, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 846, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [137, [{"start": 130, "length": 281, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 420, "length": 399, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 856, "length": 145, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1016, "length": 158, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1174, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1193, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1222, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1235, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [138, [{"start": 36, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 86, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 124, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 173, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 320, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1079, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1552, "length": 4, "messageText": "Binding element 'open' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1594, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1762, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2770, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3009, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3063, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3194, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3270, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3392, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3447, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3536, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3667, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3694, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3755, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3787, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3882, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3914, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4081, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4111, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4138, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4163, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4293, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [139, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 70, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 221, "length": 8, "messageText": "Cannot find module 'lodash' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 282, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1428, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1632, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3377, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3513, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3646, "length": 1, "messageText": "Parameter '_' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3649, "length": 3, "messageText": "Parameter 'key' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3857, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6223, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6236, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6493, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6506, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7114, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [140, [{"start": 37, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 70, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 108, "length": 11, "messageText": "Cannot find module 'react-dom' or its corresponding type declarations.", "category": 1, "code": 2307}]], [141, [{"start": 152, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 196, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 260, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 440, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 489, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1364, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1397, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1754, "length": 188, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1974, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2019, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2052, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2089, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2347, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2613, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2648, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ className: string; \"data-testid\": string; }' but required in type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'.", "relatedInformation": [{"file": "./src/modules/checkout/components/submit-button/index.tsx", "start": 236, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ className: string; \"data-testid\": string; }' is not assignable to type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'."}}, {"start": 2869, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2884, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2912, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2928, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3026, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3085, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3153, "length": 135, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4258, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4284, "length": 136, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4852, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4878, "length": 134, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6349, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6372, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6393, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6432, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6480, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6512, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6527, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6582, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [142, [{"start": 51, "length": 82, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 140, "length": 84, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 224, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 237, "length": 142, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 384, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [143, [{"start": 22, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 169, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 211, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [144, [{"start": 42, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 88, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 156, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 375, "length": 25, "messageText": "Cannot find module '@stripe/react-stripe-js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 442, "length": 19, "messageText": "Cannot find module '@stripe/stripe-js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 858, "length": 17, "messageText": "Binding element 'paymentProviderId' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 879, "length": 23, "messageText": "Binding element 'selectedPaymentOptionId' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 906, "length": 14, "messageText": "Binding element 'paymentInfoMap' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 944, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 986, "length": 7, "messageText": "Cannot find name 'process'. Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.", "category": 1, "code": 2580}, {"start": 1461, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1522, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1924, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1939, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2051, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2065, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3570, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3873, "length": 1, "messageText": "Parameter 'e' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4146, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [145, [{"start": 41, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 236, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 308, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 612, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 679, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 3074, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3107, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3623, "length": 188, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3843, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3888, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3901, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3915, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4285, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5102, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5230, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5597, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6249, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6265, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6376, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6440, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6919, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6940, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7124, "length": 161, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7771, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7792, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7811, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7863, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8230, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8265, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8278, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8324, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [146, [{"start": 136, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 177, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 231, "length": 25, "messageText": "Cannot find module '@stripe/react-stripe-js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 289, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 482, "length": 4, "messageText": "Binding element 'cart' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 505, "length": 10, "messageText": "Binding element 'dataTestId' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1879, "length": 1, "messageText": "Parameter 's' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3010, "length": 5, "messageText": "Binding element 'error' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 3017, "length": 13, "messageText": "Binding element 'paymentIntent' implicitly has an 'any' type.", "category": 1, "code": 7031}]], [147, [{"start": 49, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 143, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 572, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 605, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 966, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1039, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1106, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1511, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1528, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1634, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [148, [{"start": 48, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 282, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 326, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 387, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 644, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 698, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 936, "length": 7, "messageText": "Parameter 'address' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1343, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"start": 1374, "length": 4, "messageText": "Binding element 'cart' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1382, "length": 24, "messageText": "Binding element 'availableShippingMethods' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 2153, "length": 2, "messageText": "Parameter 'sm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2281, "length": 2, "messageText": "Parameter 'sm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2541, "length": 2, "messageText": "Parameter 'sm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2594, "length": 2, "messageText": "Parameter 'sm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3071, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3742, "length": 4, "messageText": "Parameter 'prev' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4109, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4142, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4819, "length": 197, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5052, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5101, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5147, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5182, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5228, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5332, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5354, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5475, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5495, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5514, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5575, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5757, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5848, "length": 6, "messageText": "Parameter 'option' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6675, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6882, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6988, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7018, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7047, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7147, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7335, "length": 1, "messageText": "Parameter 'v' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7441, "length": 6, "messageText": "Parameter 'option' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8505, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8712, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8817, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8849, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8880, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 9673, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 9800, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 9819, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 9836, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 9911, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 9948, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 9996, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 10094, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 10118, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 10228, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 10250, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 10271, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 10334, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 10476, "length": 1, "messageText": "Parameter 'v' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10582, "length": 6, "messageText": "Parameter 'option' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 11503, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 11717, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 11779, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 11892, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 11930, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 12224, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 12260, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 12293, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 12326, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 12603, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 12740, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 12761, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 12780, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 12811, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 13284, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 13323, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 13339, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 13457, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 13961, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 13993, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 14008, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 14063, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [149, [{"start": 149, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 821, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1104, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [150, [{"start": 45, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 172, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 528, "length": 9, "messageText": "Binding element 'className' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 539, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 561, "length": 3, "messageText": "Parameter 'ref' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1036, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1412, "length": 245, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1670, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1737, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1780, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1800, "length": 94, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1933, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1968, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [151, [{"start": 79, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 117, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 157, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 252, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"start": 523, "length": 123, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 653, "length": 144, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 878, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 894, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 908, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 922, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [152, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 65, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}]], [153, [{"start": 142, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 180, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 625, "length": 67, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 699, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 782, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 842, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 888, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 927, "length": 132, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1217, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1237, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1294, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1422, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1478, "length": 170, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1775, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1789, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1800, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [154, [{"start": 76, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 114, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 594, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 714, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 770, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 815, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 850, "length": 114, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1129, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1147, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1200, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1260, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1308, "length": 165, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1599, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1611, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [155, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 196, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 359, "length": 380, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 746, "length": 346, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1099, "length": 178, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1282, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [156, [{"start": 31, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 64, "length": 12, "messageText": "Cannot find module 'next/image' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 95, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 460, "length": 9, "messageText": "Binding element 'thumbnail' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 473, "length": 6, "messageText": "Binding element 'images' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 501, "length": 10, "messageText": "Binding element 'isFeatured' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 515, "length": 9, "messageText": "Binding element 'className' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 543, "length": 10, "messageText": "Binding element 'dataTestid' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1291, "length": 4, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"large\" | \"small\" | \"medium\" | \"full\" | \"square\" | undefined'.", "relatedInformation": [{"start": 283, "length": 4, "messageText": "The expected type comes from property 'size' which is declared here on type 'Pick<ThumbnailProps, \"size\"> & { image?: string | undefined; }'", "category": 3, "code": 6500}]}, {"start": 1739, "length": 81, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1886, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [157, [{"start": 47, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 136, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 812, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 2488, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2702, "length": 5, "messageText": "Parameter 'value' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3103, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3180, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3240, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3299, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3387, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3797, "length": 146, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3991, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4262, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4419, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [158, [{"start": 22, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 169, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 303, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 353, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 418, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 481, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 535, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 587, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 651, "length": 55, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 715, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 769, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 808, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 871, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 925, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 976, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1039, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [159, [{"start": 78, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 123, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 484, "length": 149, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 755, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 758, "length": 1, "messageText": "Parameter 'b' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 885, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 971, "length": 3, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ key: any; item: any; type: \"preview\"; currencyCode: any; }' is not assignable to type 'ItemProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'key' does not exist on type 'ItemProps'.", "category": 1, "code": 2339}]}}, {"start": 1322, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [160, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 185, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 348, "length": 169, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 524, "length": 522, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1053, "length": 161, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1221, "length": 160, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1386, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [161, [{"start": 74, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 127, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 296, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 340, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 661, "length": 4, "messageText": "Binding element 'cart' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 884, "length": 9, "messageText": "Parameter 'promotion' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 986, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1019, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1303, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1343, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1566, "length": 47, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1620, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1657, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1672, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1797, "length": 241, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2087, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2335, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2654, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ variant: \"secondary\"; \"data-testid\": string; }' but required in type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'.", "relatedInformation": [{"file": "./src/modules/checkout/components/submit-button/index.tsx", "start": 236, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ variant: \"secondary\"; \"data-testid\": string; }' is not assignable to type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'."}}, {"start": 2847, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3022, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3077, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3132, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3318, "length": 9, "messageText": "Parameter 'promotion' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3377, "length": 199, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3692, "length": 55, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5191, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5299, "length": 389, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5757, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5866, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5896, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5947, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6002, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6019, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6043, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6054, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [162, [{"start": 82, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 439, "length": 6, "messageText": "Binding element 'totals' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 616, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 628, "length": 69, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 706, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 768, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 871, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 889, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1033, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1049, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1097, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1161, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1175, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1195, "length": 157, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1466, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1484, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1510, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1572, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1586, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1604, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1766, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1782, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1797, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1846, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1896, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1914, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2057, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2073, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2122, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2186, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2201, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2221, "length": 166, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2502, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2520, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2544, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2557, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2625, "length": 84, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2718, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2729, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2745, "length": 122, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2943, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2957, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2970, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3036, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [163, [{"start": 24, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 369, "length": 92, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 468, "length": 47, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 851, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 921, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 934, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 945, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [164, [{"start": 343, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 375, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 636, "length": 95, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 890, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [165, [{"start": 103, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 162, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 203, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 243, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 798, "length": 137, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 942, "length": 77, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1028, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1202, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1219, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1226, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1572, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1583, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [166, [{"start": 136, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 486, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 551, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 616, "length": 89, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 716, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 964, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 981, "length": 87, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1152, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1208, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1302, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1326, "length": 124, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1506, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1696, "length": 5, "messageText": "Parameter 'child' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1921, "length": 146, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2580, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2710, "length": 5, "messageText": "Parameter 'child' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2754, "length": 19, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3210, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3276, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3331, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3397, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3417, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3509, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3565, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3660, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3684, "length": 263, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4025, "length": 15, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4323, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4367, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4387, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4421, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4475, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4529, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4551, "length": 69, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4637, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4660, "length": 204, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4910, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4931, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4953, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4976, "length": 202, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5231, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5252, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5274, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5297, "length": 226, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5574, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5595, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5615, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5633, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5650, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5665, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5680, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5922, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5935, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5946, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [167, [{"start": 88, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 184, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 225, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 628, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 700, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 850, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}, {"start": 1110, "length": 3, "messageText": "Parameter 'acc' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1115, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2036, "length": 102, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3085, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3154, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3190, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3208, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3302, "length": 97, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3462, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3465, "length": 1, "messageText": "Parameter 'b' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3652, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3685, "length": 185, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4354, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4435, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4502, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4583, "length": 82, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4698, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5133, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5438, "length": 167, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5698, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5736, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5773, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6087, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6122, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6155, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6483, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6512, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6559, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6582, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6663, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6735, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6842, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6885, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6913, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6941, "length": 171, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7306, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7332, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7701, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7758, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7780, "length": 73, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7872, "length": 113, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8006, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8013, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8039, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8064, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8097, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8123, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8234, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8283, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8452, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8475, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8496, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8587, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [169, [{"start": 25, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 781, "length": 5, "messageText": "Parameter 'state' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [170, [{"start": 107, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 182, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 219, "length": 20, "messageText": "Cannot find module 'react-country-flag' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 336, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 426, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1092, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1387, "length": 1, "messageText": "Parameter 'o' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1612, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1757, "length": 1, "messageText": "Parameter 'o' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1891, "length": 60, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1964, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1982, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2030, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2422, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2455, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2495, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3085, "length": 1, "messageText": "Parameter 'o' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3088, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3824, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3852, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [171, [{"start": 64, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 122, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 182, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 222, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 386, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 640, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 671, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 770, "length": 4, "messageText": "Binding element 'open' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 776, "length": 5, "messageText": "Binding element 'close' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 819, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1169, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1800, "length": 184, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2005, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2073, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2186, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2216, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2243, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2442, "length": 15, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2900, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2978, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3004, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3066, "length": 198, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3791, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4040, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4065, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4186, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4197, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [172, [{"start": 25, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 110, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 470, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 528, "length": 95, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 632, "length": 136, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 779, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 849, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 931, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 948, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 966, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1276, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1294, "length": 77, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1384, "length": 63, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1701, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2109, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2124, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2137, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2151, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [173, [{"start": 104, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 205, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 251, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 374, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 584, "length": 2, "messageText": "Parameter 'pr' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3182, "length": 5, "messageText": "Parameter 'price' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3303, "length": 9, "messageText": "Parameter 'priceRule' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3413, "length": 5, "messageText": "Parameter 'price' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4229, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4290, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4328, "length": 63, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4402, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4460, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4640, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4727, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4745, "length": 131, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4911, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5109, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5149, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5164, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5179, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5234, "length": 337, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5571, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5588, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5653, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5668, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5681, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5692, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5878, "length": 348, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6233, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6456, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6470, "length": 63, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6542, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6575, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6615, "length": 67, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6697, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6763, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6955, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7058, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7080, "length": 147, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7270, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7482, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7530, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7549, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7568, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7627, "length": 371, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7998, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8019, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8083, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8102, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8119, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8134, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8150, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8724, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8737, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 8748, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [174, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 233, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 665, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}]], [175, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 254, "length": 90, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 351, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 411, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 423, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 533, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 608, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [176, [{"start": 26, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}]], [177, [{"start": 21, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 174, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 919, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1123, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1317, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1441, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1456, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1469, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [178, [{"start": 76, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 115, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 709, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 769, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 997, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1010, "length": 83, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1177, "length": 21, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1289, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1315, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1325, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [179, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 333, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 424, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [180, [{"start": 23, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 73, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 125, "length": 89, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 221, "length": 110, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 340, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 732, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 748, "length": 105, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 971, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 982, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 993, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [181, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 833, "length": 23, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 865, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 985, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 997, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [182, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 184, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 347, "length": 340, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 694, "length": 328, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1027, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [183, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 186, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 349, "length": 416, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 772, "length": 344, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1121, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [184, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 187, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 350, "length": 169, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 526, "length": 790, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1323, "length": 185, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1513, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [185, [{"start": 34, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 87, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 144, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 482, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 820, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 832, "length": 63, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1225, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1238, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1334, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1430, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1449, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1499, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1520, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1821, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1932, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1945, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1975, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2134, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2156, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2461, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2574, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2589, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2619, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2778, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2800, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3074, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3184, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3196, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3224, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3357, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3379, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3402, "length": 260, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3683, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3797, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3810, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3838, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3932, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3958, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3978, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3996, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4034, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4047, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4118, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4134, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4169, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4207, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4223, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4240, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4288, "length": 69, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4372, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4394, "length": 14, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ href: string; route: any; \"data-testid\": string; }' but required in type 'AccountNavLinkProps'.", "relatedInformation": [{"start": 5852, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ href: string; route: any; \"data-testid\": string; }' is not assignable to type 'AccountNavLinkProps'."}}, {"start": 4615, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4635, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4657, "length": 14, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ href: string; route: any; \"data-testid\": string; }' but required in type 'AccountNavLinkProps'.", "relatedInformation": [{"start": 5852, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ href: string; route: any; \"data-testid\": string; }' is not assignable to type 'AccountNavLinkProps'."}}, {"start": 4884, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4904, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4926, "length": 14, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ href: string; route: any; \"data-testid\": string; }' but required in type 'AccountNavLinkProps'.", "relatedInformation": [{"start": 5852, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ href: string; route: any; \"data-testid\": string; }' is not assignable to type 'AccountNavLinkProps'."}}, {"start": 5159, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5179, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5201, "length": 14, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ href: string; route: any; \"data-testid\": string; }' but required in type 'AccountNavLinkProps'.", "relatedInformation": [{"start": 5852, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ href: string; route: any; \"data-testid\": string; }' is not assignable to type 'AccountNavLinkProps'."}}, {"start": 5425, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5445, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5492, "length": 144, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5679, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5703, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5721, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5737, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5752, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5765, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5776, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5862, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}]], [186, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 177, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 358, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 370, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 403, "length": 63, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 473, "length": 90, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 572, "length": 69, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 652, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 705, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 722, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 756, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 771, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 786, "length": 115, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 912, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 930, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 978, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 996, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1148, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1166, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1183, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1312, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1327, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1340, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1351, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [187, [{"start": 78, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 255, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"start": 281, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}]], [188, [{"start": 104, "length": 80, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 217, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [189, [{"start": 104, "length": 80, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 217, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [190, [{"start": 26, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 262, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 465, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 513, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 558, "length": 69, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 638, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 760, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 778, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 875, "length": 145, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1065, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1083, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1099, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1114, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1186, "length": 75, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1274, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1337, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1393, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1432, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1454, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1513, "length": 210, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1796, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1822, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1935, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1959, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1980, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2002, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2058, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2099, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2121, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2180, "length": 200, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2454, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2480, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2589, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2613, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2634, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2653, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2673, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2727, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2787, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2832, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2852, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2873, "length": 114, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3142, "length": 164, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3576, "length": 80, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3687, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3730, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3768, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3876, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3914, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4022, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4060, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4206, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4244, "length": 154, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4481, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4519, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4795, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4831, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4866, "length": 175, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5072, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5193, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5298, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5416, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5505, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5559, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5600, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5618, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5635, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5650, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5663, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5674, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6025, "length": 4, "messageText": "Parameter 'addr' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [191, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 118, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}]], [192, [{"start": 63, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}]], [193, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 181, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 344, "length": 147, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 498, "length": 147, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 650, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [194, [{"start": 35, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 75, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 122, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 953, "length": 74, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1065, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1125, "length": 242, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2440, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2455, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2561, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 2692, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2735, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2748, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2762, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2854, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2870, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2968, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 3225, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 3251, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3298, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3367, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 3393, "length": 55, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3458, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [195, [{"start": 35, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 85, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 152, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 496, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1339, "length": 206, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1554, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1598, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1629, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1647, "length": 5, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ isOpen: boolean; close: () => void; \"data-testid\": string; }' but required in type 'ModalProps'.", "relatedInformation": [{"file": "./src/modules/common/components/modal/index.tsx", "start": 358, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ isOpen: boolean; close: () => void; \"data-testid\": string; }' is not assignable to type 'ModalProps'."}}, {"start": 1826, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1888, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1942, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2467, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3105, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3626, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4243, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4298, "length": 130, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4477, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4560, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4868, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ \"data-testid\": string; }' but required in type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'.", "relatedInformation": [{"file": "./src/modules/checkout/components/submit-button/index.tsx", "start": 236, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ \"data-testid\": string; }' is not assignable to type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'."}}, {"start": 4939, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4980, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [196, [{"start": 73, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 125, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 186, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 589, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 872, "length": 6, "messageText": "Binding element 'region' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 882, "length": 7, "messageText": "Binding element 'address' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1716, "length": 268, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1993, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2539, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2646, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2673, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2694, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2714, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2819, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2839, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3009, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3043, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3058, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3112, "length": 177, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3338, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3358, "length": 188, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3625, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3643, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3656, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3671, "length": 5, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ isOpen: boolean; close: () => void; \"data-testid\": string; }' but required in type 'ModalProps'.", "relatedInformation": [{"file": "./src/modules/common/components/modal/index.tsx", "start": 358, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ isOpen: boolean; close: () => void; \"data-testid\": string; }' is not assignable to type 'ModalProps'."}}, {"start": 3852, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3889, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3984, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4041, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4695, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5517, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6163, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6964, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7019, "length": 55, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7123, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7206, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7514, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ \"data-testid\": string; }' but required in type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'.", "relatedInformation": [{"file": "./src/modules/checkout/components/submit-button/index.tsx", "start": 236, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ \"data-testid\": string; }' is not assignable to type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'."}}, {"start": 7585, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7626, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [197, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 167, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 334, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 344, "length": 6, "messageText": "Binding element 'region' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 407, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 438, "length": 67, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 591, "length": 7, "messageText": "Parameter 'address' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 733, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 744, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [198, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 57, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 666, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 734, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 787, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 835, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 849, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1049, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1060, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1129, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [199, [{"start": 23, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 62, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 293, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 500, "length": 3, "messageText": "Parameter 'acc' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 505, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 698, "length": 65, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 770, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 828, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 883, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 897, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 910, "length": 95, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1014, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1131, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1147, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1330, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1346, "length": 23, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1449, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1463, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1476, "length": 63, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1579, "length": 1, "messageText": "Parameter 'i' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1618, "length": 130, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1839, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1926, "length": 126, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2097, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2121, "length": 23, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2145, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2169, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2215, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2237, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2256, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2332, "length": 73, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2418, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2520, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2540, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2597, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2615, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2639, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2652, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2915, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2926, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [200, [{"start": 37, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 200, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 338, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 424, "length": 120, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 591, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 616, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 649, "length": 110, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 766, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 817, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 829, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 939, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 950, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1166, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1177, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [201, [{"start": 45, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 176, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 316, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 370, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 758, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 811, "length": 73, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 893, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1185, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1252, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1267, "length": 102, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1380, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1520, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ variant: \"secondary\"; className: string; }' but required in type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'.", "relatedInformation": [{"file": "./src/modules/checkout/components/submit-button/index.tsx", "start": 236, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ variant: \"secondary\"; className: string; }' is not assignable to type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'."}}, {"start": 1709, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1724, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1738, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1938, "length": 109, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2058, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2188, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2547, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2564, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2817, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2837, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [202, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 129, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 575, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 640, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 693, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 729, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 743, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 917, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 928, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 941, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1065, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1076, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [203, [{"start": 24, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 140, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 185, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 277, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 326, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 375, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 469, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 485, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 619, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 633, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 645, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 656, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [204, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 72, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 670, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 771, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1149, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1230, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1325, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1377, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1563, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1708, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [205, [{"start": 64, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 104, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 448, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 648, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 651, "length": 1, "messageText": "Parameter 'b' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 778, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 864, "length": 3, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ key: any; item: any; currencyCode: any; }' is not assignable to type 'ItemProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'key' does not exist on type 'ItemProps'.", "category": 1, "code": 2339}]}}, {"start": 1179, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [206, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 65, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 411, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 498, "length": 110, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 641, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 736, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 830, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 927, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 974, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1003, "length": 67, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1175, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1311, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1407, "length": 119, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1594, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1653, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1664, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [207, [{"start": 76, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 413, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 425, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 469, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 481, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 547, "length": 90, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 648, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 662, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 680, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 713, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 729, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 744, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 837, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 903, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 917, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 939, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 980, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1000, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1074, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1140, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1154, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1176, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1218, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1238, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1268, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1332, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1346, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1366, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1405, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1423, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1440, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1504, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1515, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1535, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1569, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1587, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1602, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1617, "length": 75, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1701, "length": 90, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1802, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1813, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1831, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1861, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1877, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1890, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1901, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [208, [{"start": 76, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 124, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 339, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 460, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 511, "length": 105, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1410, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1426, "length": 106, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1819, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1835, "length": 104, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2401, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2414, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2460, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [209, [{"start": 36, "length": 17, "messageText": "Cannot find module '@medusajs/icons' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 80, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 508, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 656, "length": 5, "messageText": "Binding element 'order' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 686, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 747, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 813, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 856, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1142, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1155, "length": 121, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1463, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1474, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [210, [{"start": 157, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 189, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}]], [211, [{"start": 27, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 82, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 123, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 219, "length": 11, "messageText": "Cannot find module 'react-dom' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 298, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"start": 418, "length": 5, "messageText": "Cannot find namespace 'React'.", "category": 1, "code": 2503}, {"start": 952, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1020, "length": 48, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1077, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1119, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1170, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1188, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1322, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1394, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1471, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1486, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1501, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1840, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1853, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2323, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2356, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2885, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2905, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3313, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3370, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3385, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3404, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3723, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3740, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3799, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [212, [{"start": 63, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 195, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 387, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1103, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1423, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1720, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1752, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [213, [{"start": 72, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 271, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 536, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 548, "length": 7, "messageText": "Binding element 'regions' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 647, "length": 6, "messageText": "Parameter 'region' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 700, "length": 7, "messageText": "Parameter 'country' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 981, "length": 4, "messageText": "Parameter 'addr' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1668, "length": 7, "messageText": "Parameter 'country' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1808, "length": 72, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1889, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1969, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1985, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2015, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2031, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2159, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2175, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2252, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2268, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2283, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2297, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2364, "length": 74, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2445, "length": 67, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2764, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2817, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3354, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4010, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4542, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4974, "length": 17, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4992, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5034, "length": 6, "messageText": "Parameter 'option' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5042, "length": 1, "messageText": "Parameter 'i' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5089, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5178, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5254, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5286, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [214, [{"start": 63, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 195, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 390, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1161, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1481, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1772, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1804, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [215, [{"start": 63, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 195, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 386, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1164, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1492, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1955, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1987, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [216, [{"start": 63, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 192, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 232, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 372, "length": 8, "messageText": "Binding element 'customer' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 671, "length": 101, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 849, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 901, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1103, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1731, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1763, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [217, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 428, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 858, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 924, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 977, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1014, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1028, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1255, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1266, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1279, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1674, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1685, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1732, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [218, [{"start": 45, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 639, "length": 98, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 744, "length": 47, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 835, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 847, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1028, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1039, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1107, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2134, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2219, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2762, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2779, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ className: string; \"data-testid\": string; }' but required in type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'.", "relatedInformation": [{"file": "./src/modules/checkout/components/submit-button/index.tsx", "start": 236, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ className: string; \"data-testid\": string; }' is not assignable to type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'."}}, {"start": 2892, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2906, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3016, "length": 110, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3153, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3179, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3191, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [219, [{"start": 342, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 537, "length": 102, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 646, "length": 47, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 705, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 717, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 849, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 860, "length": 45, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 914, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1437, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1528, "length": 12, "code": 2741, "category": 1, "messageText": "Property 'children' is missing in type '{ \"data-testid\": string; className: string; }' but required in type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'.", "relatedInformation": [{"file": "./src/modules/checkout/components/submit-button/index.tsx", "start": 236, "length": 8, "messageText": "'children' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ \"data-testid\": string; className: string; }' is not assignable to type '{ children: React.ReactNode; variant?: \"primary\" | \"secondary\" | \"transparent\" | \"danger\" | null | undefined; className?: string | undefined; \"data-testid\"?: string | undefined; }'."}}, {"start": 1643, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1657, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1763, "length": 151, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1941, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1967, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1979, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [220, [{"start": 39, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 345, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 564, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [221, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}]], [222, [{"start": 22, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 175, "length": 78, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 323, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 373, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 438, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 501, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 555, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 607, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 671, "length": 55, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 735, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 789, "length": 28, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 828, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 891, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 974, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1025, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1088, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [223, [{"start": 48, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 93, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 136, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 149, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 209, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 243, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 258, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 292, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 305, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 316, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [224, [{"start": 40, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 104, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [225, [{"start": 67, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 116, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 159, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 173, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 233, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 271, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 286, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 324, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 337, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 351, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 416, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 454, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 469, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 507, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 520, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 534, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 594, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 633, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 648, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 686, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 699, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 713, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 781, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 795, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 855, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 898, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 913, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 956, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 969, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 980, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [226, [{"start": 212, "length": 29, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 292, "length": 22, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 348, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 359, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [227, [{"start": 22, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 373, "length": 23, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 403, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 447, "length": 71, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 529, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 594, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 668, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 724, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 795, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 864, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 885, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 907, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 976, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 995, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1014, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1034, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1091, "length": 55, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1161, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1353, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1567, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1723, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1897, "length": 34, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1956, "length": 54, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2033, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2358, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2375, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2392, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2512, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2527, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2540, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2551, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [229, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 254, "length": 84, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 345, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 405, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 417, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 569, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 644, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [230, [{"start": 64, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 113, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 420, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 432, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 553, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1227, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1230, "length": 1, "messageText": "Parameter 'b' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1357, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1443, "length": 3, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ key: any; item: any; currencyCode: any; }' is not assignable to type 'ItemProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'key' does not exist on type 'ItemProps'.", "category": 1, "code": 2339}]}}, {"start": 1758, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [231, [{"start": 46, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 362, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 832, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1272, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [232, [{"start": 30, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 169, "length": 102, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 639, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 725, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 736, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [233, [{"start": 38, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 181, "length": 60, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 248, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 486, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 499, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 713, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 724, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [234, [{"start": 271, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 440, "length": 23, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 470, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 578, "length": 71, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 662, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 923, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 942, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 983, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1120, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1225, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1287, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1306, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1323, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1354, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1403, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1427, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1438, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [235, [{"start": 176, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 208, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}]], [236, [{"start": 26, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 96, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 215, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 285, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 324, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 339, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 378, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 391, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 402, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [237, [{"start": 242, "length": 145, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 445, "length": 16, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 507, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 527, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [238, [{"start": 70, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 116, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 874, "length": 110, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1085, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [239, [{"start": 34, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 105, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 961, "length": 227, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1207, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1303, "length": 106, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1424, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3127, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3184, "length": 63, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3268, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3279, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [240, [{"start": 1424, "length": 144, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1631, "length": 15, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1720, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1756, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [241, [{"start": 25, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 68, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 578, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1231, "length": 137, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1446, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1479, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1610, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1974, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2008, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2061, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2075, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2127, "length": 40, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2180, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2205, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2220, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2289, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2340, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2428, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2452, "length": 15, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2619, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2655, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2671, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3061, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3072, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [242, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 57, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 227, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 814, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [243, [{"start": 25, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 378, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 701, "length": 87, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 834, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 867, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 914, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 936, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 950, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1330, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1341, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [244, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 57, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 248, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 904, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [245, [{"start": 60, "length": 59, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 126, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 165, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 178, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 217, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 230, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 269, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 307, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 322, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 360, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 373, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 384, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [246, [{"start": 141, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 153, "length": 86, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 248, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 290, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 333, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 350, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 389, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 406, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 450, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 467, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 506, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 521, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 536, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 578, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 621, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 638, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 677, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 694, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 738, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 755, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 794, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 811, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 855, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 872, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 915, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 932, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 971, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 986, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 999, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1012, "length": 61, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1082, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1124, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1167, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1184, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1223, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1240, "length": 44, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1284, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1299, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1344, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1355, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [247, [{"start": 50, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 127, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 188, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 239, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 254, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 315, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 367, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 405, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 424, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 462, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 481, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 519, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 536, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 553, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 591, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 606, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 619, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 633, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 694, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 745, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 760, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 821, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 873, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 911, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 930, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 968, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 987, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1025, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1042, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1059, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1097, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1112, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1125, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1139, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1200, "length": 51, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1251, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1266, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1327, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1379, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1417, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1436, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1474, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1493, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1531, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1548, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1565, "length": 38, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1603, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1618, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1631, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1642, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [248, [{"start": 340, "length": 72, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 419, "length": 55, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 483, "length": 55, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 664, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 677, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 688, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [250, [{"start": 114, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 277, "length": 66, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 805, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [251, [{"start": 41, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 248, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 469, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 589, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 627, "length": 49, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 689, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1082, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1101, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1277, "length": 70, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2060, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2079, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2096, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2120, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2167, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [252, [{"start": 24, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 78, "length": 14, "messageText": "Cannot find module 'next/headers' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 566, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 883, "length": 47, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 937, "length": 110, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1119, "length": 144, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1406, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1422, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1442, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1483, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1856, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1869, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1880, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [253, [{"start": 161, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 193, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}]], [254, [{"start": 124, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 164, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1168, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1601, "length": 30, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2287, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2384, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [255, [{"start": 25, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 96, "length": 122, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 223, "length": 254, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 482, "length": 252, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 739, "length": 159, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 903, "length": 180, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1088, "length": 180, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1273, "length": 179, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1457, "length": 179, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1641, "length": 180, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1826, "length": 180, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2011, "length": 180, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2196, "length": 161, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2362, "length": 255, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2622, "length": 253, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2880, "length": 160, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3045, "length": 160, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3210, "length": 161, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3376, "length": 454, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3835, "length": 161, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4001, "length": 159, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4165, "length": 672, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4842, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4888, "length": 185, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5078, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5087, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5133, "length": 186, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5324, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5333, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5379, "length": 187, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5571, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5580, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5626, "length": 187, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5818, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5827, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5873, "length": 185, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6063, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6072, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6118, "length": 186, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6309, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6318, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6331, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6373, "length": 164, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6544, "length": 11, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6562, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6604, "length": 164, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6775, "length": 11, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6793, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6835, "length": 164, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7006, "length": 11, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7024, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7066, "length": 165, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7238, "length": 11, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7256, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7298, "length": 165, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7470, "length": 11, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7488, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7530, "length": 165, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7702, "length": 11, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7718, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 7728, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [256, [{"start": 30, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 340, "length": 77, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 448, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 870, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1282, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1382, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1393, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [257, [{"start": 87, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 393, "length": 77, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 501, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1185, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1196, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [258, [{"start": 88, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 395, "length": 77, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 503, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1187, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1198, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [259, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 70, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 103, "length": 12, "messageText": "Cannot find module 'next/image' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 260, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 310, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1183, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1194, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [260, [{"start": 47, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}]], [261, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 64, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 97, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 376, "length": 6, "messageText": "Binding element 'option' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 386, "length": 7, "messageText": "Binding element 'current' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 397, "length": 12, "messageText": "Binding element 'updateOption' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 413, "length": 5, "messageText": "Binding element 'title' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 437, "length": 10, "messageText": "Binding element 'dataTestId' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 451, "length": 8, "messageText": "Binding element 'disabled' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 522, "length": 1, "messageText": "Parameter 'v' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 553, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 599, "length": 26, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 639, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 653, "length": 102, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 786, "length": 1, "messageText": "Parameter 'v' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 825, "length": 572, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1428, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1468, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1479, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [262, [{"start": 20, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 124, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 496, "length": 60, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 577, "length": 47, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 631, "length": 139, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 809, "length": 114, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 975, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 989, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1066, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1082, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1128, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1148, "length": 172, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1378, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1396, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1411, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1509, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1542, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [263, [{"start": 35, "length": 19, "messageText": "Cannot find module '@headlessui/react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 83, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 139, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 438, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 889, "length": 7, "messageText": "Binding element 'product' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 900, "length": 7, "messageText": "Binding element 'variant' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 911, "length": 7, "messageText": "Binding element 'options' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 922, "length": 13, "messageText": "Binding element 'updateOptions' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 939, "length": 7, "messageText": "Binding element 'inStock' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 950, "length": 15, "messageText": "Binding element 'handleAddToCart' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 969, "length": 8, "messageText": "Binding element 'isAdding' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 981, "length": 4, "messageText": "Binding element 'show' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 989, "length": 15, "messageText": "Binding element 'optionsDisabled' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1421, "length": 127, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1845, "length": 202, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2060, "length": 43, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2118, "length": 33, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2166, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2188, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2195, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2252, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2389, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2415, "length": 50, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2543, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2571, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2615, "length": 195, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2882, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2906, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2949, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2954, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2990, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3009, "length": 112, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3344, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3421, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3576, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3634, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4113, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4130, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4165, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4592, "length": 76, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4710, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4765, "length": 80, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5388, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5455, "length": 237, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5741, "length": 9, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5769, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5794, "length": 37, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5916, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6010, "length": 6, "messageText": "Parameter 'option' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6086, "length": 21, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6501, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6586, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6634, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6719, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 6736, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [264, [{"start": 140, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 181, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 363, "length": 8, "messageText": "Cannot find module 'lodash' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 398, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 469, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1624, "length": 1, "messageText": "Parameter 'v' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1907, "length": 4, "messageText": "Parameter 'prev' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2106, "length": 1, "messageText": "Parameter 'v' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3313, "length": 56, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3378, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3448, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3532, "length": 6, "messageText": "Parameter 'option' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3588, "length": 21, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3983, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4064, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 4092, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 5075, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [265, [{"start": 40, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 94, "length": 14, "messageText": "Cannot find module 'next/headers' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 398, "length": 46, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 724, "length": 77, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 880, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 891, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [266, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 184, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 347, "length": 150, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 504, "length": 537, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1046, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [267, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 192, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 355, "length": 161, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 523, "length": 161, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 691, "length": 161, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 859, "length": 289, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1155, "length": 161, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1323, "length": 387, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1715, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [268, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 187, "length": 156, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 350, "length": 169, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 526, "length": 352, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 885, "length": 168, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1060, "length": 352, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1417, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [269, [{"start": 26, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 77, "length": 27, "messageText": "Cannot find module '@radix-ui/react-accordion' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 123, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 795, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 965, "length": 5, "messageText": "Binding element 'title' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 974, "length": 8, "messageText": "Binding element 'subtitle' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 986, "length": 11, "messageText": "Binding element 'description' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1001, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1013, "length": 9, "messageText": "Binding element 'className' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1113, "length": 11, "messageText": "Binding element 'triggerable' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1397, "length": 31, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1439, "length": 58, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1510, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1637, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1790, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1947, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2252, "length": 85, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2402, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2436, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2451, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2602, "length": 208, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2817, "length": 25, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2851, "length": 149, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3009, "length": 214, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3230, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3241, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [270, [{"start": 238, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 604, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 937, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1026, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1074, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1125, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1175, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1193, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1233, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1253, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1299, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1314, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1331, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1349, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1398, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1418, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1476, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1491, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1508, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1526, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1562, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1582, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1626, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1641, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1656, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1671, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1721, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1739, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1777, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1797, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1846, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1861, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1878, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1896, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1938, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1958, "length": 3, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2142, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2157, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2172, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2185, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2196, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2257, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2305, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2356, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2436, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2454, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2499, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2519, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2687, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2702, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2717, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2732, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2807, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2825, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2873, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2893, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3047, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3062, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3077, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3092, "length": 42, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3164, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3182, "length": 32, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3226, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3246, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3469, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3484, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3499, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3512, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 3523, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [271, [{"start": 122, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 799, "length": 1, "messageText": "Parameter 't' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1161, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1209, "length": 62, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1280, "length": 55, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1371, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1387, "length": 57, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1512, "length": 4, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1523, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1537, "length": 86, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1671, "length": 21, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1761, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1785, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1795, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [272, [{"start": 26, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 74, "length": 14, "messageText": "Cannot find module '@medusajs/ui' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 308, "length": 23, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 338, "length": 64, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1086, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1097, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [273, [{"start": 186, "length": 41, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 234, "length": 67, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 310, "length": 52, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 362, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 377, "length": 68, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 456, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 509, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 526, "length": 53, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 579, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 594, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 607, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 620, "length": 93, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 760, "length": 16, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 826, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 850, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 860, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [274, [{"start": 76, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}]], [275, [{"start": 32, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 601, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 707, "length": 17, "messageText": "Cannot find module '@medusajs/types' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 908, "length": 7, "messageText": "Binding element 'product' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 919, "length": 6, "messageText": "Binding element 'region' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 929, "length": 11, "messageText": "Binding element 'countryCode' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 1034, "length": 151, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1194, "length": 108, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1399, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1414, "length": 39, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1520, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1535, "length": 109, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1994, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2007, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2020, "length": 117, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 2296, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [276, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 57, "length": 17, "messageText": "Cannot find module 'next/navigation' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 458, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 482, "length": 1, "messageText": "Parameter 'c' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 605, "length": 7, "messageText": "Parameter 'country' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 947, "length": 11, "messageText": "Parameter 'countryData' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 997, "length": 7, "messageText": "Parameter 'product' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1125, "length": 5, "messageText": "Parameter 'param' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [277, [{"start": 25, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 564, "length": 137, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 747, "length": 24, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 780, "length": 36, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 827, "length": 35, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 874, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 888, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1111, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 1122, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]], [278, [{"start": 25, "length": 6, "messageText": "Cannot find module 'next' or its corresponding type declarations.", "category": 1, "code": 2307}]], [279, [{"start": 18, "length": 7, "messageText": "Cannot find module 'react' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 191, "length": 8, "messageText": "Binding element 'children' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 223, "length": 5, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 249, "length": 27, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 286, "length": 7, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}, {"start": 315, "length": 6, "messageText": "JSX element implicitly has type 'any' because no interface 'JSX.IntrinsicElements' exists.", "category": 1, "code": 7026}]]], "affectedFilesPendingEmit": [164, 118, 120, 198, 189, 210, 202, 191, 217, 221, 187, 188, 228, 229, 235, 242, 244, 174, 175, 249, 253, 257, 258, 256, 181, 276, 278, 111, 112, 85, 125, 192, 89, 90, 91, 87, 92, 93, 94, 95, 96, 100, 88, 260, 169, 101, 102, 103, 106, 104, 86, 105, 107, 108, 99, 84, 211, 185, 197, 195, 196, 219, 199, 200, 190, 213, 214, 215, 216, 212, 218, 201, 186, 220, 150, 232, 157, 233, 234, 230, 159, 231, 241, 138, 141, 134, 133, 161, 135, 146, 144, 143, 126, 121, 145, 147, 139, 148, 140, 149, 163, 243, 162, 136, 151, 127, 97, 131, 119, 152, 153, 154, 113, 194, 132, 137, 266, 123, 114, 130, 129, 267, 122, 183, 115, 116, 184, 124, 155, 268, 128, 160, 182, 193, 179, 178, 180, 168, 167, 165, 170, 117, 171, 166, 279, 172, 203, 204, 205, 250, 206, 207, 251, 208, 254, 255, 252, 209, 259, 264, 263, 261, 265, 177, 176, 262, 269, 270, 271, 156, 275, 274, 272, 173, 224, 142, 222, 225, 223, 158, 245, 246, 247, 226, 236, 227, 248, 237, 273, 239, 238, 98, 277, 240, 109, 110], "version": "5.9.2"}