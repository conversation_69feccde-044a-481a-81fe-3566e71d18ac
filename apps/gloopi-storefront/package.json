{"name": "@gloopi/storefront", "version": "1.0.3", "private": true, "author": "Gloopi Indonesia", "description": "Gloopi B2B Storefront - Next.js frontend", "keywords": ["medusa-storefront"], "scripts": {"dev": "next dev -p 8000", "build": "next build", "start": "next start -p 8000", "lint": "next lint", "type-check": "tsc --noEmit", "analyze": "ANALYZE=true next build"}, "dependencies": {"@headlessui/react": "^2.2.0", "@medusajs/js-sdk": "latest", "@medusajs/ui": "latest", "@radix-ui/react-accordion": "^1.2.1", "@stripe/react-stripe-js": "^1.7.2", "@stripe/stripe-js": "^1.29.0", "awilix": "^8.0.1", "lodash": "^4.17.21", "next": "^15.3.1", "pg": "^8.11.3", "qs": "^6.12.1", "react": "19.0.0-rc-66855b96-20241106", "react-country-flag": "^3.1.0", "react-dom": "19.0.0-rc-66855b96-20241106", "server-only": "^0.0.1", "tailwindcss-radix": "^2.8.0", "webpack": "^5"}, "devDependencies": {"@babel/core": "^7.17.5", "@medusajs/types": "latest", "@medusajs/ui-preset": "latest", "@types/lodash": "^4.14.195", "@types/node": "17.0.21", "@types/pg": "^8.11.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-instantsearch-dom": "^6.12.3", "ansi-colors": "^4.1.3", "autoprefixer": "^10.4.2", "babel-loader": "^8.2.3", "eslint": "8.10.0", "eslint-config-next": "15.0.3", "postcss": "^8.4.8", "prettier": "^2.8.8", "tailwindcss": "^3.0.23", "typescript": "^5.6.2"}, "packageManager": "yarn@4.4.0", "resolutions": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}, "overrides": {"react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106"}}